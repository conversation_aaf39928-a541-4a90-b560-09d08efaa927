@model OrderFlowCore.Web.ViewModels.SupervisorOrdersViewModel
@{
    ViewData["Title"] = "متابعة السجلات الخاصة";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">متابعة السجلات الخاصة</h2>
            <!-- Message Container for validation and operation feedback -->
            <div id="followUpMessageContainer"></div>
            <!-- Follow-up Records Card -->
            <div class="card mb-4">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i> متابعة السجلات الخاصة</h5>
                </div>
                <div class="card-body">
                    <form asp-action="Index" method="get">
                        <button type="submit" name="toggleRecords" value="true" class="btn btn-dark mb-3">
                            @(Model.ShowRecordsPanel ? "إخفاء السجلات" : "إظهار السجلات")
                        </button>
                    </form>
                    @if (Model.ShowRecordsPanel)
                    {
                        <!-- Search and Export/Import Controls -->
                        <div class="row mb-3">
                            <div class="col-md-6 mb-2 mb-md-0">
                                <input type="text" id="followUpSearch" class="form-control" placeholder="بحث في السجلات..." autocomplete="off" />
                            </div>
                            <div class="col-md-6 text-end">
                                <button type="button" class="btn btn-outline-primary me-2" id="exportCsvBtn"><i class="fas fa-file-export"></i> تصدير CSV</button>
                                <label class="btn btn-outline-secondary mb-0">
                                    <i class="fas fa-file-import"></i> استيراد CSV
                                    <input type="file" id="importCsvInput" accept=".csv" hidden />
                                </label>
                            </div>
                        </div>
                        <!-- Follow-up Records Table and Add/Edit/Delete -->
                        <form id="addFollowUpForm" class="row g-2 mb-3" autocomplete="off">
                            @Html.AntiForgeryToken()
                            <div class="col-md-3">
                                <input id="addCivilRegistry" name="CivilRegistry" class="form-control" placeholder="السجل المدني" required minlength="2" maxlength="20" />
                            </div>
                            <div class="col-md-3">
                                <input id="addOwnerName" name="OwnerName" class="form-control" placeholder="اسم صاحب الطلب" required minlength="2" maxlength="50" />
                            </div>
                            <div class="col-md-3">
                                <input id="addSpecialProcedure" name="SpecialProcedure" class="form-control" placeholder="الإجراء المطلوب" required minlength="2" maxlength="100" />
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-success w-100">إضافة للمتابعة</button>
                            </div>
                        </form>
                        <table class="table table-bordered table-striped" id="followUpTable">
                            <thead>
                                <tr>
                                    <th>السجل المدني</th>
                                    <th>اسم صاحب الطلب</th>
                                    <th>الإجراء المطلوب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var rec in Model.FollowUpRecords)
                                {
                                    <tr class="followup-row" data-civil="@rec.CivilRecord" data-owner="@rec.OwnerName" data-proc="@rec.SpecialProcedure">
                                        <td>@rec.CivilRecord</td>
                                        <td>@rec.OwnerName</td>
                                        <td>@rec.SpecialProcedure</td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-primary edit-btn">تعديل</button>
                                            <button type="button" class="btn btn-sm btn-danger delete-btn ms-1">حذف</button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                        <!-- Pagination Controls -->
                        <div id="followUpPagination" class="d-flex justify-content-center mt-3"></div>
                    }
                </div>
            </div>

            <!-- Edit Modal -->
            <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">تعديل السجل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <form id="editFollowUpForm" autocomplete="off">
                        @Html.AntiForgeryToken()
                        <input type="hidden" id="editCivilRegistry" name="CivilRecord" />
                        <div class="mb-3">
                            <label class="form-label">اسم صاحب الطلب</label>
                            <input type="text" id="editOwnerName" name="OwnerName" class="form-control" required minlength="2" maxlength="50" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الإجراء المطلوب</label>
                            <input type="text" id="editSpecialProcedure" name="SpecialProcedure" class="form-control" required minlength="2" maxlength="100" />
                        </div>
                        <button type="submit" class="btn btn-success">حفظ التعديل</button>
                    </form>
                  </div>
                </div>
              </div>
            </div>

            <!-- Delete Confirmation Modal -->
            <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <p>هل أنت متأكد أنك تريد حذف هذا السجل؟</p>
                    <div id="deleteModalRecordInfo" class="mb-2"></div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                  </div>
                </div>
              </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/supervisorOrders.js"></script>
} 
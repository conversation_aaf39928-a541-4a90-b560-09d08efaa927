// supervisorOrders.js
// Handles follow-up records AJAX and UI logic for SupervisorOrders

document.addEventListener("DOMContentLoaded", function () {
    // Initialize OrderDetailsModule for ProcessOrder page
    OrderDetailsModule.init({
        showLoading: function() {
            var loading = document.getElementById('loading');
            if (loading) loading.style.display = '';
            var details = document.getElementById('orderDetails');
            if (details) details.style.display = 'none';
        },
        hideLoading: function() {
            var loading = document.getElementById('loading');
            if (loading) loading.style.display = 'none';
        },
        showMessage: function(message, type) {
            var alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
            var msg = document.getElementById('messageContainer');
            if (msg) msg.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
        },
        showOrderDetails: function() {
            var details = document.getElementById('orderDetails');
            if (details) {
                details.style.display = '';
                details.classList.add('fade-in');
            }
            var quickActions = document.getElementById('quickActions');
            if (quickActions) quickActions.style.display = '';
        },
        hideOrderDetails: function() {
            var details = document.getElementById('orderDetails');
            if (details) details.style.display = 'none';
            var quickActions = document.getElementById('quickActions');
            if (quickActions) quickActions.style.display = 'none';
        }
    });

    // Only attach order selection handler if #orderSelect exists (ProcessOrder view)
    var orderSelect = document.getElementById('orderSelect');
    if (orderSelect) {
        orderSelect.addEventListener('change', function() {
            var orderId = this.value;
            if (orderId && orderId !== '') {
                OrderDetailsModule.loadOrderDetails(orderId, '/SupervisorOrders/GetOrderDetails');
            } else {
                OrderDetailsModule.hideOrderDetails();
            }
        });
    }

    // ProcessOrder: validation and confirmation modals
    function showProcessMsg(message, type) {
        SharedUtils.showToast(message, type);
    }
    // Confirm Order
    var confirmOrderBtn = document.getElementById('confirmOrderBtn');
    var confirmOrderModalBtn = document.getElementById('confirmOrderModalBtn');
    var confirmOrderForm = document.getElementById('confirmOrderForm');
    if (confirmOrderBtn && confirmOrderModalBtn && confirmOrderForm) {
        confirmOrderBtn.addEventListener('click', function (e) {
            e.preventDefault();
            var orderId = confirmOrderForm.querySelector('input[name="orderId"]').value;
            if (!orderId) {
                showProcessMsg('يرجى اختيار رقم الطلب.', 'danger');
                return;
            }
            var modal = new bootstrap.Modal(document.getElementById('confirmOrderModal'));
            modal.show();
        });
        confirmOrderModalBtn.addEventListener('click', function () {
            confirmOrderForm.submit();
        });
    }
    // Needs Action
    var needsActionBtn = document.getElementById('needsActionBtn');
    var needsActionModalBtn = document.getElementById('needsActionModalBtn');
    var needsActionForm = document.getElementById('needsActionForm');
    var actionRequiredInput = document.getElementById('actionRequiredInput');
    if (needsActionBtn && needsActionModalBtn && needsActionForm && actionRequiredInput) {
        needsActionBtn.addEventListener('click', function (e) {
            e.preventDefault();
            var orderId = needsActionForm.querySelector('input[name="orderId"]').value;
            var val = actionRequiredInput.value.trim();
            if (!orderId) {
                showProcessMsg('يرجى اختيار رقم الطلب.', 'danger');
                return;
            }
            if (!val || val.length < 2 || val.length > 100) {
                showProcessMsg('يرجى إدخال الإجراءات المطلوبة بشكل صحيح (2-100 حرف).', 'danger');
                return;
            }
            document.getElementById('needsActionModalReason').textContent = val;
            var modal = new bootstrap.Modal(document.getElementById('needsActionModal'));
            modal.show();
        });
        needsActionModalBtn.addEventListener('click', function () {
            needsActionForm.submit();
        });
    }
    // Reject Order
    var rejectOrderBtn = document.getElementById('rejectOrderBtn');
    var rejectOrderModalBtn = document.getElementById('rejectOrderModalBtn');
    var rejectOrderForm = document.getElementById('rejectOrderForm');
    var rejectReasonInput = document.getElementById('rejectReasonInput');
    if (rejectOrderBtn && rejectOrderModalBtn && rejectOrderForm && rejectReasonInput) {
        rejectOrderBtn.addEventListener('click', function (e) {
            e.preventDefault();
            var orderId = rejectOrderForm.querySelector('input[name="orderId"]').value;
            var val = rejectReasonInput.value.trim();
            if (!orderId) {
                showProcessMsg('يرجى اختيار رقم الطلب.', 'danger');
                return;
            }
            if (!val || val.length < 2 || val.length > 100) {
                showProcessMsg('يرجى إدخال سبب الإعادة بشكل صحيح (2-100 حرف).', 'danger');
                return;
            }
            document.getElementById('rejectOrderModalReason').textContent = val;
            var modal = new bootstrap.Modal(document.getElementById('rejectOrderModal'));
            modal.show();
        });
        rejectOrderModalBtn.addEventListener('click', function () {
            rejectOrderForm.submit();
        });
    }

    // ProcessOrder: UX enhancements
    var orderSelect = document.getElementById('orderSelect');
    var confirmOrderBtn = document.getElementById('confirmOrderBtn');
    var needsActionBtn = document.getElementById('needsActionBtn');
    var rejectOrderBtn = document.getElementById('rejectOrderBtn');
    var actionRequiredInput = document.getElementById('actionRequiredInput');
    var rejectReasonInput = document.getElementById('rejectReasonInput');
    var loadingOverlay = null;

    // 1. Disable action buttons when no order is selected
    function setActionButtonsState(enabled) {
        if (confirmOrderBtn) confirmOrderBtn.disabled = !enabled;
        if (needsActionBtn) needsActionBtn.disabled = !enabled;
        if (rejectOrderBtn) rejectOrderBtn.disabled = !enabled;
        if (actionRequiredInput) actionRequiredInput.disabled = !enabled;
        if (rejectReasonInput) rejectReasonInput.disabled = !enabled;
    }
    function getSelectedOrderId() {
        return orderSelect ? orderSelect.value : '';
    }
    if (orderSelect) {
        setActionButtonsState(!!getSelectedOrderId());
        orderSelect.addEventListener('change', function() {
            setActionButtonsState(!!getSelectedOrderId());
            // Highlight selected option
            for (var i = 0; i < orderSelect.options.length; i++) {
                orderSelect.options[i].style.backgroundColor = '';
            }
            if (orderSelect.selectedIndex > 0) {
                orderSelect.options[orderSelect.selectedIndex].style.backgroundColor = '#d1e7dd';
            }
        });
        // Initial highlight
        if (orderSelect.selectedIndex > 0) {
            orderSelect.options[orderSelect.selectedIndex].style.backgroundColor = '#d1e7dd';
        }
    }

    // 2. Show loading spinner overlay on form submit
    function showLoadingOverlay() {
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.style.position = 'fixed';
            loadingOverlay.style.top = 0;
            loadingOverlay.style.left = 0;
            loadingOverlay.style.width = '100vw';
            loadingOverlay.style.height = '100vh';
            loadingOverlay.style.background = 'rgba(0,0,0,0.2)';
            loadingOverlay.style.zIndex = 2000;
            loadingOverlay.innerHTML = '<div class="d-flex justify-content-center align-items-center" style="height:100vh;"><div class="spinner-border text-primary" style="width:3rem;height:3rem;" role="status"><span class="visually-hidden">جاري المعالجة...</span></div></div>';
            document.body.appendChild(loadingOverlay);
        }
    }
    function hideLoadingOverlay() {
        if (loadingOverlay) {
            loadingOverlay.remove();
            loadingOverlay = null;
        }
    }

    // 3. Reset action inputs after success
    function resetActionInputs() {
        if (actionRequiredInput) actionRequiredInput.value = '';
        if (rejectReasonInput) rejectReasonInput.value = '';
    }

    // 4. Keyboard accessibility for modals (Enter triggers confirm)
    function addModalEnterKey(modalId, confirmBtnId) {
        var modal = document.getElementById(modalId);
        var confirmBtn = document.getElementById(confirmBtnId);
        if (modal && confirmBtn) {
            modal.addEventListener('shown.bs.modal', function() {
                modal.focus();
            });
            modal.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    confirmBtn.click();
                }
            });
        }
    }
    addModalEnterKey('confirmOrderModal', 'confirmOrderModalBtn');
    addModalEnterKey('needsActionModal', 'needsActionModalBtn');
    addModalEnterKey('rejectOrderModal', 'rejectOrderModalBtn');

    // 5. Show loading overlay and reset inputs on form submit (ProcessOrder)
    var confirmOrderForm = document.getElementById('confirmOrderForm');
    var needsActionForm = document.getElementById('needsActionForm');
    var rejectOrderForm = document.getElementById('rejectOrderForm');
    if (confirmOrderForm) {
        confirmOrderForm.addEventListener('submit', function() {
            showLoadingOverlay();
        });
    }
    if (needsActionForm) {
        needsActionForm.addEventListener('submit', function() {
            showLoadingOverlay();
        });
    }
    if (rejectOrderForm) {
        rejectOrderForm.addEventListener('submit', function() {
            showLoadingOverlay();
        });
    }
    // Hide overlay and reset inputs after navigation (on page load)
    window.addEventListener('pageshow', function() {
        hideLoadingOverlay();
        resetActionInputs();
    });
});

(function () {
    // CSRF token for AJAX
    var token = $("input[name='__RequestVerificationToken']").first().val();

    function showAlert(message, type) {
        SharedUtils.showToast(message, type);
    }

    // Only run follow-up records logic if #addFollowUpForm exists (FollowUpRecords view)
    if ($('#addFollowUpForm').length) {
        // Add follow-up record with validation
        $('#addFollowUpForm').on('submit', function (e) {
            e.preventDefault();
            var civil = $('#addCivilRegistry').val().trim();
            var owner = $('#addOwnerName').val().trim();
            var proc = $('#addSpecialProcedure').val().trim();
            if (!civil || civil.length < 2 || civil.length > 20) {
                showAlert('يرجى إدخال السجل المدني بشكل صحيح (2-20 حرف).', 'danger');
                return;
            }
            if (!owner || owner.length < 2 || owner.length > 50) {
                showAlert('يرجى إدخال اسم صاحب الطلب بشكل صحيح (2-50 حرف).', 'danger');
                return;
            }
            if (!proc || proc.length < 2 || proc.length > 100) {
                showAlert('يرجى إدخال الإجراء المطلوب بشكل صحيح (2-100 حرف).', 'danger');
                return;
            }
            var data = { CivilRecord: civil, OwnerName: owner, SpecialProcedure: proc };
            $.ajax({
                url: window.addFollowUpRecordUrl || '/SupervisorOrders/AddFollowUpRecordAjax',
                type: 'POST',
                contentType: 'application/json',
                headers: { 'RequestVerificationToken': token },
                data: JSON.stringify(data),
                success: function (res) {
                    if (res.success) {
                        $('#followUpTable tbody').append(`<tr data-civil="${data.CivilRecord}" data-owner="${data.OwnerName}" data-proc="${data.SpecialProcedure}">
                            <td>${data.CivilRecord}</td>
                            <td>${data.OwnerName}</td>
                            <td>${data.SpecialProcedure}</td>
                            <td><button type='button' class='btn btn-sm btn-primary edit-btn'>تعديل</button> <button type='button' class='btn btn-sm btn-danger delete-btn ms-1'>حذف</button></td>
                        </tr>`);
                        showAlert(res.message, 'success');
                        $('#addFollowUpForm')[0].reset();
                    } else {
                        showAlert(res.message, 'danger');
                    }
                },
                error: function () { showAlert('خطأ في الاتصال بالخادم', 'danger'); }
            });
        });

        // Edit (open modal)
        $(document).on('click', '.edit-btn', function () {
            var row = $(this).closest('tr');
            $('#editCivilRegistry').val(row.data('civil'));
            $('#editOwnerName').val(row.data('owner'));
            $('#editSpecialProcedure').val(row.data('proc'));
            var modal = new bootstrap.Modal(document.getElementById('editModal'));
            modal.show();
        });

        // Edit (submit) with validation
        $('#editFollowUpForm').on('submit', function (e) {
            e.preventDefault();
            var civil = $('#editCivilRegistry').val().trim();
            var owner = $('#editOwnerName').val().trim();
            var proc = $('#editSpecialProcedure').val().trim();
            if (!owner || owner.length < 2 || owner.length > 50) {
                showAlert('يرجى إدخال اسم صاحب الطلب بشكل صحيح (2-50 حرف).', 'danger');
                return;
            }
            if (!proc || proc.length < 2 || proc.length > 100) {
                showAlert('يرجى إدخال الإجراء المطلوب بشكل صحيح (2-100 حرف).', 'danger');
                return;
            }
            var data = { CivilRecord: civil, OwnerName: owner, SpecialProcedure: proc };
            $.ajax({
                url: window.editFollowUpRecordUrl || '/SupervisorOrders/EditFollowUpRecordAjax',
                type: 'POST',
                contentType: 'application/json',
                headers: { 'RequestVerificationToken': token },
                data: JSON.stringify(data),
                success: function (res) {
                    if (res.success) {
                        var row = $(`#followUpTable tr[data-civil='${data.CivilRecord}']`);
                        row.find('td:eq(1)').text(data.OwnerName);
                        row.find('td:eq(2)').text(data.SpecialProcedure);
                        row.data('owner', data.OwnerName);
                        row.data('proc', data.SpecialProcedure);
                        showAlert(res.message, 'success');
                        bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                    } else {
                        showAlert(res.message, 'danger');
                    }
                },
                error: function () { showAlert('خطأ في الاتصال بالخادم', 'danger'); }
            });
        });

        // Delete follow-up record with Bootstrap modal confirmation
        var deleteCivil = null;
        var deleteRow = null;
        $(document).on('click', '.delete-btn', function () {
            var row = $(this).closest('tr');
            deleteCivil = row.data('civil');
            deleteRow = row;
            // Show info in modal
            $('#deleteModalRecordInfo').html(
                `<strong>السجل المدني:</strong> ${row.data('civil')}<br>` +
                `<strong>اسم صاحب الطلب:</strong> ${row.data('owner')}<br>` +
                `<strong>الإجراء المطلوب:</strong> ${row.data('proc')}`
            );
            var modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        });
        $('#confirmDeleteBtn').on('click', function () {
            if (!deleteCivil || !deleteRow) return;
            $.ajax({
                url: window.deleteFollowUpRecordUrl || '/SupervisorOrders/DeleteFollowUpRecordAjax',
                type: 'POST',
                contentType: 'application/json',
                headers: { 'RequestVerificationToken': token },
                data: JSON.stringify({ CivilRecord: deleteCivil }),
                success: function (res) {
                    if (res.success) {
                        deleteRow.remove();
                        showAlert(res.message, 'success');
                    } else {
                        showAlert(res.message, 'danger');
                    }
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                    deleteCivil = null;
                    deleteRow = null;
                },
                error: function () {
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                    deleteCivil = null;
                    deleteRow = null;
                }
            });
        });
    }

    // FollowUpRecords: search, pagination, export/import, inline edit, animation
    var $table = $('#followUpTable');
    if ($table.length) {
        var $rows = $table.find('tbody tr');
        var pageSize = 10;
        var currentPage = 1;
        function filterRows() {
            var q = $('#followUpSearch').val().toLowerCase();
            $rows = $table.find('tbody tr').filter(function() {
                var text = $(this).text().toLowerCase();
                return text.indexOf(q) !== -1;
            });
            paginateRows();
        }
        function paginateRows() {
            var total = $rows.length;
            var totalPages = Math.ceil(total / pageSize) || 1;
            if (currentPage > totalPages) currentPage = totalPages;
            $table.find('tbody tr').hide();
            $rows.slice((currentPage-1)*pageSize, currentPage*pageSize).fadeIn(200);
            // Pagination controls
            var $p = $('#followUpPagination').empty();
            if (totalPages > 1) {
                for (var i=1; i<=totalPages; i++) {
                    var btn = $('<button type="button" class="btn btn-sm mx-1"></button>').text(i);
                    if (i === currentPage) btn.addClass('btn-primary'); else btn.addClass('btn-outline-primary');
                    (function(page){ btn.click(function(){ currentPage=page; paginateRows(); }); })(i);
                    $p.append(btn);
                }
            }
        }
        $('#followUpSearch').on('input', function(){ currentPage=1; filterRows(); });
        filterRows();
        // Animated add/edit/delete
        $table.on('DOMNodeInserted', 'tr', function(){ $(this).hide().fadeIn(300); });
        // Inline editing
        $table.on('dblclick', '.followup-row td:not(:last-child)', function(){
            var $td = $(this);
            var $tr = $td.closest('tr');
            var idx = $td.index();
            var oldVal = $td.text();
            var input = $('<input type="text" class="form-control form-control-sm" />').val(oldVal);
            $td.empty().append(input);
            input.focus();
            input.on('blur keydown', function(e){
                if (e.type==='blur' || (e.type==='keydown' && e.key==='Enter')) {
                    var newVal = input.val().trim();
                    if (newVal && newVal !== oldVal) {
                        // Update model and send AJAX
                        var civil = $tr.data('civil');
                        var owner = idx===1 ? newVal : $tr.data('owner');
                        var proc = idx===2 ? newVal : $tr.data('proc');
                        $.ajax({
                            url: window.editFollowUpRecordUrl || '/SupervisorOrders/EditFollowUpRecordAjax',
                            type: 'POST',
                            contentType: 'application/json',
                            headers: { 'RequestVerificationToken': token },
                            data: JSON.stringify({ CivilRecord: civil, OwnerName: owner, SpecialProcedure: proc }),
                            success: function (res) {
                                if (res.success) {
                                    $td.text(newVal);
                                    if (idx===1) $tr.data('owner', newVal);
                                    if (idx===2) $tr.data('proc', newVal);
                                    SharedUtils.showToast('تم التعديل بنجاح', 'success');
                                } else {
                                    $td.text(oldVal);
                                    SharedUtils.showToast(res.message, 'danger');
                                }
                            },
                            error: function () { $td.text(oldVal); SharedUtils.showToast('خطأ في الاتصال بالخادم', 'danger'); }
                        });
                    } else {
                        $td.text(oldVal);
                    }
                }
            });
        });
        // Export CSV
        $('#exportCsvBtn').on('click', function(){
            var csv = 'CivilRecord,OwnerName,SpecialProcedure\n';
            $table.find('tbody tr:visible').each(function(){
                var $tr = $(this);
                csv += [$tr.data('civil'), $tr.data('owner'), $tr.data('proc')].map(function(x){return '"'+(x||'')+'"';}).join(',')+'\n';
            });
            var blob = new Blob([csv], {type:'text/csv'});
            var url = URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url; a.download = 'FollowUpRecords.csv';
            document.body.appendChild(a); a.click(); document.body.removeChild(a);
            URL.revokeObjectURL(url);
            SharedUtils.showToast('تم تصدير السجلات بنجاح', 'success');
        });
        // Import CSV
        $('#importCsvInput').on('change', function(e){
            var file = e.target.files[0];
            if (!file) return;
            var reader = new FileReader();
            reader.onload = function(ev){
                var lines = ev.target.result.split(/\r?\n/).filter(Boolean);
                if (lines.length < 2) { SharedUtils.showToast('الملف فارغ أو غير صالح', 'danger'); return; }
                var added = 0;
                for (var i=1; i<lines.length; i++) {
                    var parts = lines[i].split(',').map(function(x){return x.replace(/^"|"$/g,'');});
                    if (parts.length>=3) {
                        // Simulate add (could use AJAX for real add)
                        var data = { CivilRecord: parts[0], OwnerName: parts[1], SpecialProcedure: parts[2] };
                        $.ajax({
                            url: window.addFollowUpRecordUrl || '/SupervisorOrders/AddFollowUpRecordAjax',
                            type: 'POST',
                            contentType: 'application/json',
                            headers: { 'RequestVerificationToken': token },
                            data: JSON.stringify(data),
                            success: function (res) {
                                if (res.success) {
                                    $table.find('tbody').append(`<tr class="followup-row" data-civil="${data.CivilRecord}" data-owner="${data.OwnerName}" data-proc="${data.SpecialProcedure}">
                                        <td>${data.CivilRecord}</td>
                                        <td>${data.OwnerName}</td>
                                        <td>${data.SpecialProcedure}</td>
                                        <td><button type='button' class='btn btn-sm btn-primary edit-btn'>تعديل</button> <button type='button' class='btn btn-sm btn-danger delete-btn ms-1'>حذف</button></td>
                                    </tr>`);
                                    added++;
                                }
                            }
                        });
                    }
                }
                setTimeout(function(){
                    filterRows();
                    SharedUtils.showToast('تم استيراد '+added+' سجل بنجاح', 'success');
                }, 1000);
            };
            reader.readAsText(file);
        });
    }
})(); 
@model OrderFlowCore.Web.ViewModels.SupervisorOrdersViewModel
@{
    ViewData["Title"] = "معالجة الطلبات";
}

@Html.AntiForgeryToken()

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">معالجة الطلبات</h2>
            <!-- Message Container for validation and operation feedback -->
            <div id="processOrderMessageContainer"></div>
            <!-- Order Selection Section -->
            <div class="order-select-container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label for="orderSelect" class="form-label text-white">📋 رقم الطلب:</label>
                        @Html.DropDownListFor(m => m.SelectedOrderId, Model.OrderNumbers, "اختر الطلب من القائمة",
                            new { @class = "form-select", @id = "orderSelect"})
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <a asp-action="DownloadAttachments" asp-route-orderId="@Model.SelectedOrderId" class="btn btn-primary ms-2">
                                <i class="fas fa-download"></i> تحميل مرفقات الطلب
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Section -->
            <div id="quickActions" class="quick-actions mt-4">
                <h4 class="text-center mb-4">🚀 الإجراءات السريعة</h4>
                <div class="row mb-3 p-3 bg-light rounded border">
                    <div class="col-md-4 mb-2 mb-md-0">
                        <form id="confirmOrderForm" asp-action="ConfirmOrder" method="post">
                            @Html.AntiForgeryToken()
                            <input type="hidden" name="orderId" value="@Model.SelectedOrderId" />
                            <button type="button" class="btn btn-success w-100" id="confirmOrderBtn"><span class="action-icon">✅</span>اعتماد الطلب</button>
                        </form>
                    </div>
                    <div class="col-md-4 mb-2 mb-md-0">
                        <form id="needsActionForm" asp-action="NeedsAction" method="post">
                            @Html.AntiForgeryToken()
                            <input type="hidden" name="orderId" value="@Model.SelectedOrderId" />
                            <input type="text" name="actionRequired" id="actionRequiredInput" class="form-control mb-2" placeholder="الإجراءات المطلوبة" required minlength="2" maxlength="100" />
                            <button type="button" class="btn btn-warning w-100" id="needsActionBtn"><span class="action-icon">⚠️</span>يتطلب إجراءات</button>
                        </form>
                    </div>
                    <div class="col-md-4">
                        <form id="rejectOrderForm" asp-action="RejectOrder" method="post">
                            @Html.AntiForgeryToken()
                            <input type="hidden" name="orderId" value="@Model.SelectedOrderId" />
                            <input type="text" name="rejectReason" id="rejectReasonInput" class="form-control mb-2" placeholder="سبب الإعادة" required minlength="2" maxlength="100" />
                            <button type="button" class="btn btn-danger w-100" id="rejectOrderBtn"><span class="action-icon">❌</span>إعادة الطلب</button>
                        </form>
                    </div>
                </div>
            </div>

            <div id="messageContainer" class="mt-3"></div>
            <div id="actionRequireMessageContainer" class="mt-3"></div>
            <!-- Loading -->
            <div id="loading" class="loading text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل تفاصيل الطلب...</p>
            </div>
            <!-- Order Details Section -->
            @await Html.PartialAsync("_OrderDetailsPartial")
        </div>
    </div>
</div>

<!-- Confirm Order Modal -->
<div class="modal fade" id="confirmOrderModal" tabindex="-1" aria-labelledby="confirmOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmOrderModalLabel">تأكيد الاعتماد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من اعتماد هذا الطلب؟</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmOrderModalBtn">تأكيد</button>
            </div>
        </div>
    </div>
</div>
<!-- Needs Action Modal -->
<div class="modal fade" id="needsActionModal" tabindex="-1" aria-labelledby="needsActionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="needsActionModalLabel">تأكيد الإجراء المطلوب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إرسال هذا الطلب كـ "يتطلب إجراءات"؟</p>
                <div id="needsActionModalReason" class="text-muted"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" id="needsActionModalBtn">تأكيد</button>
            </div>
        </div>
    </div>
</div>
<!-- Reject Order Modal -->
<div class="modal fade" id="rejectOrderModal" tabindex="-1" aria-labelledby="rejectOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectOrderModalLabel">تأكيد إعادة الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إعادة هذا الطلب؟</p>
                <div id="rejectOrderModalReason" class="text-muted"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="rejectOrderModalBtn">تأكيد</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/shared-utils.js"></script>
    <script src="~/js/orderDetailsModule.js"></script>
    <script>
        // Show success/error messages from TempData
        @if (TempData["SuccessMessage"] != null)
        {
            <text>OrderDetailsModule.showMessage('@TempData["SuccessMessage"]', 'success');</text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>OrderDetailsModule.showMessage('@TempData["ErrorMessage"]', 'error');</text>
        }
    </script>
} 
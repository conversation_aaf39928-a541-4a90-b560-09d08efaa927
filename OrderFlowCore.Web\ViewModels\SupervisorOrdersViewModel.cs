using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Web.ViewModels
{
    public class SupervisorOrdersViewModel
    {
        // Dropdown for order numbers
        public List<SelectListItem> OrderNumbers { get; set; } = new List<SelectListItem>();
        public int? SelectedOrderId { get; set; }

        // Order details
        public OrderDetailsDto OrderDetails { get; set; }

        // Follow-up records
        public List<SupervisorsFollowUpDto> FollowUpRecords { get; set; } = new List<SupervisorsFollowUpDto>();

        // Add/Edit form fields
        public string CivilRegistry { get; set; }
        public string OwnerName { get; set; }
        public string SpecialProcedure { get; set; }
        public int? EditRecordId { get; set; }
        public string EditCivilRegistry { get; set; }
        public string EditOwnerName { get; set; }
        public string EditSpecialProcedure { get; set; }

        // Messages
        public string SuccessMessage { get; set; }
        public string ErrorMessage { get; set; }
        public bool ShowRecordsPanel { get; set; } = false;
    }
} 
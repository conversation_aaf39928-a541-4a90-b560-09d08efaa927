{"Version": 1, "WorkspaceRootPath": "E:\\Projects\\abozyad\\OrderFlowCore\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.core\\entities\\supervisorsfollowup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|solutionrelative:orderflowcore.core\\entities\\supervisorsfollowup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\supervisorsfollowupservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\supervisorsfollowupservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.infrastructure\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|solutionrelative:orderflowcore.infrastructure\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.infrastructure\\data\\unitofwork.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|solutionrelative:orderflowcore.infrastructure\\data\\unitofwork.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\viewmodels\\supervisorordersviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\viewmodels\\supervisorordersviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\supervisororderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\supervisororderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\shared\\_sidebarpartial.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\shared\\_sidebarpartial.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "SupervisorsFollowUp.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\SupervisorsFollowUp.cs", "RelativeDocumentMoniker": "OrderFlowCore.Core\\Entities\\SupervisorsFollowUp.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\SupervisorsFollowUp.cs", "RelativeToolTip": "OrderFlowCore.Core\\Entities\\SupervisorsFollowUp.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T20:34:37.361Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DependencyInjection.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\DependencyInjection.cs", "RelativeDocumentMoniker": "OrderFlowCore.Infrastructure\\DependencyInjection.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\DependencyInjection.cs", "RelativeToolTip": "OrderFlowCore.Infrastructure\\DependencyInjection.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T20:32:43.222Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DependencyInjection.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\DependencyInjection.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\DependencyInjection.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\DependencyInjection.cs", "RelativeToolTip": "OrderFlowCore.Application\\DependencyInjection.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAcwB4AAABWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T20:31:51.838Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SupervisorsFollowUpService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorsFollowUpService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\SupervisorsFollowUpService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorsFollowUpService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\SupervisorsFollowUpService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwBYAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T20:23:28.62Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "UnitOfWork.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\Data\\UnitOfWork.cs", "RelativeDocumentMoniker": "OrderFlowCore.Infrastructure\\Data\\UnitOfWork.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\Data\\UnitOfWork.cs", "RelativeToolTip": "OrderFlowCore.Infrastructure\\Data\\UnitOfWork.cs", "ViewState": "AgIAABgAAAAAAAAAAAAwwCcAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T20:23:25.533Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "SupervisorOrdersViewModel.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\ViewModels\\SupervisorOrdersViewModel.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\ViewModels\\SupervisorOrdersViewModel.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\ViewModels\\SupervisorOrdersViewModel.cs", "RelativeToolTip": "OrderFlowCore.Web\\ViewModels\\SupervisorOrdersViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T20:23:21.589Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "SupervisorOrdersController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T20:23:14.766Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "_SidebarPartial.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Shared\\_SidebarPartial.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\Shared\\_SidebarPartial.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Shared\\_SidebarPartial.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\Shared\\_SidebarPartial.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-15T20:22:52.999Z", "EditorCaption": ""}]}]}]}
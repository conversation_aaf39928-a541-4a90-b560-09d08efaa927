using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Application.DTOs;
using System;
using OrderFlowCore.Application.Interfaces.Services;

namespace OrderFlowCore.Infrastructure.Data
{
    public class OrderRepository : IOrderRepository
    {
        private readonly ApplicationDbContext _context;
        private readonly ISupervisorService _supervisorService;

        public OrderRepository(ApplicationDbContext context, ISupervisorService supervisorService)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _supervisorService = supervisorService ?? throw new ArgumentNullException(nameof(supervisorService));
        }

        #region Basic CRUD Operations

        public async Task<int> AddAsync(OrdersTable order)
        {
            if (order == null)
                throw new ArgumentNullException(nameof(order));

            await _context.OrdersTables.AddAsync(order);
            return order.Id;
        }

        public async Task<int> UpdateAsync(OrdersTable order)
        {
            if (order == null)
                throw new ArgumentNullException(nameof(order));

            _context.OrdersTables.Update(order);
            return order.Id;
        }

        public async Task<OrdersTable> GetByIdAsync(int id)
        {
            return await _context.OrdersTables.FindAsync(id);
        }

        public async Task<List<OrdersTable>> GetAllAsync()
        {
            return await _context.OrdersTables.ToListAsync();
        }

        #endregion

        #region Manager and Coordinator Operations

        public async Task<List<OrdersTable>> GetPendingOrdersForDirectMangerAsync()
        {
            return await _context.OrdersTables
                .Where(o => o.OrderStatus == OrderStatus.DM ||
                           o.OrderStatus == OrderStatus.ReturnedByAssistantManager)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId)
        {
            var status = assistantManagerId.ToOrderStatus();

            return await _context.OrdersTables
                .Where(o => o.OrderStatus == status ||
                           o.OrderStatus == OrderStatus.ReturnedByCoordinator)
                .OrderByDescending(o => o.CreatedAt)
                .ThenByDescending(o => o.Id)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetHRCoordinatorPendingOrders()
        {
            var allowedStatuses = new[]
            {
                OrderStatus.B,
                OrderStatus.ReturnedBySupervisor,
                OrderStatus.ActionRequired
            };

            return await _context.OrdersTables
                .Where(o => allowedStatuses.Contains(o.OrderStatus))
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        #endregion

        #region Status and Filtering Operations

        public async Task<List<OrdersTable>> GetOrdersByStatusesAsync(string[] statuses)
        {
            if (statuses == null || !statuses.Any())
                return new List<OrdersTable>();

            return await _context.OrdersTables
                .Where(o => statuses.Contains(o.OrderStatus.ToString()))
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<OrdersTable?> GetOrderByIdAsync(int orderId)
        {
            return await _context.OrdersTables.FindAsync(orderId);
        }

        #endregion

        #region Supervisor Operations

        public async Task<List<SupervisorRejectionDto>> GetSupervisorRejectionsAsync(int orderId)
        {
            var order = await GetByIdAsync(orderId);
            if (order == null)
                return new List<SupervisorRejectionDto>();

            return _supervisorService.ExtractRejections(order);
        }

        public async Task UpdateSupervisorStatusesAsync(
            OrdersTable order,
            List<string> selectedSupervisors,
            string statusWithDate)
        {
            if (order == null)
                throw new ArgumentNullException(nameof(order));

            if (selectedSupervisors == null || !selectedSupervisors.Any())
                return;

            _supervisorService.UpdateSupervisorStatuses(order, selectedSupervisors, statusWithDate);
            _context.OrdersTables.Update(order);
        }

        public async Task ClearSupervisorStatusesAsync(OrdersTable order)
        {
            if (order == null)
                throw new ArgumentNullException(nameof(order));

            _supervisorService.ClearUnderImplementationStatuses(order);
            _context.OrdersTables.Update(order);
        }

        #endregion

        #region Restore Operations

        public async Task<List<RestorableOrderDto>> GetRestorableOrdersAsync(string searchTerm, string filter)
        {
            var query = BuildRestorableOrdersQuery(searchTerm, filter);

            var orders = await query
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();

            return orders.Select(MapToRestorableOrderDto).ToList();
        }

        public async Task<RestoreDetailsDto> GetRestoreOrderDetailsAsync(int orderId)
        {
            var order = await GetByIdAsync(orderId);
            if (order == null)
                return null;

            var assignedSupervisors = _supervisorService.GetAssignedSupervisors(order);
            var transferDate = ExtractTransferDate(order.ConfirmedByCoordinator);

            return new RestoreDetailsDto
            {
                CurrentStatus = order.OrderStatus.ToDisplayString(),
                TransferDate = transferDate,
                AssignedSupervisors = string.Join(", ", assignedSupervisors),
                SupervisorCount = assignedSupervisors.Count
            };
        }

        #endregion

        #region Private Helper Methods

        private IQueryable<OrdersTable> BuildRestorableOrdersQuery(string searchTerm, string filter)
        {
            var query = _context.OrdersTables
                .Where(o => o.OrderStatus == OrderStatus.C);

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(o =>
                    o.Id.ToString().Contains(searchTerm) ||
                    o.EmployeeName.Contains(searchTerm));
            }

            return ApplyDateFilter(query, filter);
        }

        private IQueryable<OrdersTable> ApplyDateFilter(IQueryable<OrdersTable> query, string filter)
        {
            var today = DateTime.Today;

            return filter?.ToLower() switch
            {
                "today" => query.Where(o => o.CreatedAt.Date == today),
                "week" => query.Where(o => o.CreatedAt.Date >= today.AddDays(-(int)today.DayOfWeek)),
                "month" => query.Where(o => o.CreatedAt.Date >= new DateTime(today.Year, today.Month, 1)),
                _ => query
            };
        }

        private RestorableOrderDto MapToRestorableOrderDto(OrdersTable order)
        {
            return new RestorableOrderDto
            {
                Id = order.Id,
                OrderNumber = order.Id.ToString(),
                EmployeeName = order.EmployeeName,
                Department = order.Department,
                OrderDate = order.CreatedAt.ToString("yyyy-MM-dd"),
                OrderStatus = order.OrderStatus,
                DisplayText = $"{order.Id} | {order.EmployeeName} | {order.Department}"
            };
        }

        private string ExtractTransferDate(string confirmedByCoordinator)
        {
            if (string.IsNullOrEmpty(confirmedByCoordinator))
                return string.Empty;

            var parts = confirmedByCoordinator.Split('|');
            return parts.Length > 0 ? parts[0] : string.Empty;
        }

        #endregion
    }

    #region Supporting Services

    #endregion
}